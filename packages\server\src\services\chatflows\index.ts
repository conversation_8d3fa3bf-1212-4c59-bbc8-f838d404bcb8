import { removeFolderFromStorage } from 'flowise-components'
import { StatusCodes } from 'http-status-codes'
import { Brackets, In, QueryRunner } from 'typeorm'
import { ChatflowType, IReactFlowObject } from '../../Interface'
import { FLOWISE_COUNTER_STATUS, FLOWISE_METRIC_COUNTERS } from '../../Interface.Metrics'
import { ChatFlow } from '../../database/entities/ChatFlow'
import { ChatMessage } from '../../database/entities/ChatMessage'
import { ChatMessageFeedback } from '../../database/entities/ChatMessageFeedback'
import { UpsertHistory } from '../../database/entities/UpsertHistory'
import { User, UserRole } from '../../database/entities/User'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getErrorMessage } from '../../errors/utils'
import documentStoreService from '../../services/documentstore'
import { constructGraphs, getAppVersion, getEndingNodes, getTelemetryFlowObj, isFlowValidForStream } from '../../utils'
import { containsBase64File, updateFlowDataWithFilePaths } from '../../utils/fileRepository'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { utilGetUploadsConfig } from '../../utils/getUploadsConfig'
import logger from '../../utils/logger'
import userSevice from '../user'
import { GroupUsers } from '../../database/entities/GroupUser'
import { LogService } from './LogService'

// Check if chatflow valid for streaming
const checkIfChatflowIsValidForStreaming = async (chatflowId: string): Promise<any> => {
  try {
    const appServer = getRunningExpressApp()
    //**
    const chatflow = await appServer.AppDataSource.getRepository(ChatFlow).findOneBy({
      id: chatflowId
    })
    if (!chatflow) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, `Chatflow ${chatflowId} not found`)
    }

    /*** Get Ending Node with Directed Graph  ***/
    const flowData = chatflow.flowData
    const parsedFlowData: IReactFlowObject = JSON.parse(flowData)
    const nodes = parsedFlowData.nodes
    const edges = parsedFlowData.edges
    const { graph, nodeDependencies } = constructGraphs(nodes, edges)

    const endingNodes = getEndingNodes(nodeDependencies, graph, nodes)

    let isStreaming = false
    for (const endingNode of endingNodes) {
      const endingNodeData = endingNode.data
      const isEndingNode = endingNodeData?.outputs?.output === 'EndingNode'
      // Once custom function ending node exists, flow is always unavailable to stream
      if (isEndingNode) {
        return { isStreaming: false }
      }
      isStreaming = isFlowValidForStream(nodes, endingNodeData)
    }

    // If it is a Multi/Sequential Agents, always enable streaming
    if (endingNodes.filter((node) => node.data.category === 'Multi Agents' || node.data.category === 'Sequential Agents').length > 0) {
      return { isStreaming: true }
    }

    const dbResponse = { isStreaming: isStreaming }
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.checkIfChatflowIsValidForStreaming - ${getErrorMessage(error)}`
    )
  }
}

// Check if chatflow valid for uploads
const checkIfChatflowIsValidForUploads = async (chatflowId: string): Promise<any> => {
  try {
    const dbResponse = await utilGetUploadsConfig(chatflowId)
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.checkIfChatflowIsValidForUploads - ${getErrorMessage(error)}`
    )
  }
}

const deleteChatflow = async (chatflowId: string, req?: any): Promise<any> => {
  try {
    const appServer = getRunningExpressApp()
    const agent = await appServer.AppDataSource.getRepository(ChatFlow).findOneBy({ id: chatflowId })
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).delete({ id: chatflowId })

    let username = 'unknown'
    if (req?.user?.id) {
      try {
        const user = await appServer.AppDataSource.getRepository(User).findOne({
          where: { id: req.user.id }
        })
        username = user?.username || 'unknown'
      } catch (error) {
        logger.warn('Failed to fetch username for userId:', req.user.id, error)
        username = 'unknown'
      }
    }
    await LogService.createLog({
      username: username,
      action: 'DELETE',
      target_type: 'agent',
      target_id: chatflowId,
      target_name: agent?.name || ''
    })
    try {
      // Delete all uploads corresponding to this chatflow
      await removeFolderFromStorage(chatflowId)
      await documentStoreService.updateDocumentStoreUsage(chatflowId, undefined)

      // Delete all chat messages
      await appServer.AppDataSource.getRepository(ChatMessage).delete({ chatflowid: chatflowId })

      // Delete all chat feedback
      await appServer.AppDataSource.getRepository(ChatMessageFeedback).delete({ chatflowid: chatflowId })

      // Delete all upsert history
      await appServer.AppDataSource.getRepository(UpsertHistory).delete({ chatflowid: chatflowId })
    } catch (e) {
      logger.error(`[server]: Error deleting file storage for chatflow ${chatflowId}: ${e}`)
    }
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: chatflowsService.deleteChatflow - ${getErrorMessage(error)}`)
  }
}

const getControlChatflowsOfAdmin = async (req: any): Promise<any[]> => {
  try {
    const { user } = req
    if (!user.id) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
    }

    const appServer = getRunningExpressApp()
    const foundUser = await appServer.AppDataSource.getRepository(User).findOneBy({ id: user.id })
    if (!foundUser) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
    }

    if (foundUser.role !== UserRole.MASTER_ADMIN) {
      throw new InternalFlowiseError(StatusCodes.FORBIDDEN, 'Error: documentStoreServices.getAllDocumentStores - Access denied')
    }

    const type = req.query?.type as ChatflowType
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).find({
      where: { type },
      relations: ['user']
    })
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getAllPublicChatflows - ${getErrorMessage(error)}`
    )
  }
}

const getControlChatflowsOfAdminGroup = async (req: any): Promise<any[]> => {
  try {
    const { user, query } = req
    const { groupname } = query
    const type = req.query?.type as ChatflowType

    if (!user.id) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
    }

    const appServer = getRunningExpressApp()
    const foundUser = await appServer.AppDataSource.getRepository(User).findOneBy({ id: user.id })
    if (!foundUser) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
    }

    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow)
      .createQueryBuilder('cf')
      .leftJoinAndSelect('cf.user', 'user')
      .where('cf.type = :type', { type })
      .andWhere('(user.groupname = :groupname OR cf.groupname = :groupname)', { groupname })
      .getMany()

    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getAllPublicChatflows - ${getErrorMessage(error)}`
    )
  }
}

const getAllPublicChatflows = async (req: any): Promise<any[]> => {
  try {
    const type = req.query?.type as ChatflowType
    const appServer = getRunningExpressApp()
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).find({
      where: { isPublic: true, type },
      relations: ['user']
    })
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getAllPublicChatflows - ${getErrorMessage(error)}`
    )
  }
}

const getAllChatflows = async (req: any) => {
  try {
    const { user } = req
    const userId = req.query.userId || user.id
    const type = req.query?.type as ChatflowType
    const page = parseInt(req.query?.page as string) || 1
    const pageSize = parseInt(req.query?.pageSize as string) || 20
    const searchQuery = req.query?.searchQuery as string
    const isPublic = req.query?.isPublic

    if (!userId) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: chatflowsService.getAllChatflows - User not found')
    }

    const appServer = getRunningExpressApp()
    const foundUser = await appServer.AppDataSource.getRepository(User).findOneBy({ id: userId })
    if (!foundUser) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: chatflowsService.getAllChatflows - User not found')
    }

    const role = foundUser.role
    let query = appServer.AppDataSource.getRepository(ChatFlow).createQueryBuilder('cf').leftJoinAndSelect('cf.user', 'user')

    // Role-based access control
    if (role === UserRole.MASTER_ADMIN) {
      // Master admin can see all flows
    } else if (role === UserRole.SITE_ADMIN) {
      const userGroups = await userSevice.getGroupsByGroupName(foundUser.groupname)
      const groupNames = userGroups.map((group) => group?.groupname).filter(Boolean)
      query = query.where('(cf.userId = :userId OR cf.groupname IN (:...groupNames))', {
        userId: foundUser.id,
        groupNames
      })
      // Site admin can see all flows of their own groups
    } else if (role === UserRole.ADMIN) {
      // Admin can see their own flows and public flows
      query = query.where('(cf.userId = :userId OR (cf.isPublic = :isPublic AND cf.groupname = :groupName))', {
        userId: foundUser.id,
        groupName: foundUser.groupname,
        isPublic: true
      })
    } else {
      // Regular users can see only their own flows and public flows
      query = query.where('(cf.userId = :userId OR (cf.isPublic = :isPublic AND cf.groupname = :groupName))', {
        userId: foundUser.id,
        isPublic: true,
        groupName: foundUser.groupname
      })
    }

    if (type) {
      query = query.andWhere('cf.type = :type', { type })
    }

    if (isPublic !== 'undefined') {
      query = query.andWhere('cf.isPublic = :isPublic', { isPublic: isPublic === 'true' })
    }

    if (searchQuery) {
      query = query.andWhere(
        new Brackets((qb) => {
          qb.where('LOWER(user.username) LIKE :username', { username: `%${searchQuery.trim().toLowerCase()}%` }).orWhere(
            'LOWER(cf.name) LIKE :chatflowName',
            {
              chatflowName: `%${searchQuery.trim().toLowerCase()}%`
            }
          )
        })
      )
    }

    const totalCount = await query.getCount()

    query = query
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .orderBy('cf.updatedDate', 'DESC')

    const chatflows = await query.getMany()

    return {
      data: chatflows,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: chatflowsService.getAllChatflows - ${getErrorMessage(error)}`)
  }
}

const getPersonalChatflows = async (req: any) => {
  try {
    const type = req.query?.type as ChatflowType
    const isPublic = req.query?.isPublic as string
    const page = parseInt(req.query?.page as string) || 1
    const pageSize = parseInt(req.query?.pageSize as string) || 10
    const { user } = req

    if (!user.id) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: chatflowsService.getPersonalChatflows - User not found')
    }

    const appServer = getRunningExpressApp()
    const foundUser = await appServer.AppDataSource.getRepository(User).findOneBy({ id: user.id })
    if (!foundUser) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: chatflowsService.getPersonalChatflows - User not found')
    }

    // Query to get only chatflows created by the current user
    let query = appServer.AppDataSource.getRepository(ChatFlow)
      .createQueryBuilder('cf')
      .leftJoinAndSelect('cf.user', 'user')
      .where('cf.userId = :userId', { userId: foundUser.id })

    // Filter by type if provided
    if (type) {
      query = query.andWhere('cf.type = :type', { type })
    }

    if (isPublic !== 'undefined') {
      query = query.andWhere('cf.isPublic = :isPublic', { isPublic: isPublic === 'true' })
    }

    // Get total count for pagination
    const totalCount = await query.getCount()

    // Apply pagination
    query = query
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .orderBy('cf.updatedDate', 'DESC')

    const chatflows = await query.getMany()

    return {
      data: chatflows,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    }
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getPersonalChatflows - ${getErrorMessage(error)}`
    )
  }
}

const getChatflowByApiKey = async (apiKeyId: string, keyonly?: unknown): Promise<any> => {
  try {
    // Here we only get chatflows that are bounded by the apikeyid and chatflows that are not bounded by any apikey
    const appServer = getRunningExpressApp()
    let query = appServer.AppDataSource.getRepository(ChatFlow)
      .createQueryBuilder('cf')
      .where('cf.apikeyid = :apikeyid', { apikeyid: apiKeyId })
    if (keyonly === undefined) {
      query = query.orWhere('cf.apikeyid IS NULL').orWhere('cf.apikeyid = ""')
    }

    const dbResponse = await query.orderBy('cf.name', 'ASC').getMany()
    if (dbResponse.length < 1) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, `Chatflow not found in the database!`)
    }
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getChatflowByApiKey - ${getErrorMessage(error)}`
    )
  }
}

const getChatflowById = async (chatflowId: string): Promise<ChatFlow> => {
  try {
    const appServer = getRunningExpressApp()
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).findOne({
      where: { id: chatflowId },
      relations: ['user', 'user.group']
    })
    if (!dbResponse) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, `Chatflow ${chatflowId} not found in the database!`)
    }
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: chatflowsService.getChatflowById - ${getErrorMessage(error)}`)
  }
}

const getChatflowByIds = async (chatflowIds: string[]): Promise<ChatFlow[]> => {
  try {
    const appServer = getRunningExpressApp()
    const chatflows = await appServer.AppDataSource.getRepository(ChatFlow).find({
      where: { id: In(chatflowIds) }
    })
    if (!chatflows) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, `Chatflows not found in the database!`)
    }
    return chatflows
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getChatflowByIds - ${getErrorMessage(error)}`
    )
  }
}

const saveChatflow = async (newChatFlow: ChatFlow): Promise<any> => {
  try {
    const appServer = getRunningExpressApp()
    let dbResponse: ChatFlow
    let isUpdate = false
    console.log('🚀 ~ index.ts:701 ~ saveChatflow ~ newChatFlow:', newChatFlow)
    if (newChatFlow.id) {
      // Nếu có id thì là update
      isUpdate = true
    }
    if (containsBase64File(newChatFlow)) {
      // we need a 2-step process, as we need to save the chatflow first and then update the file paths
      // this is because we need the chatflow id to create the file paths

      // step 1 - save with empty flowData
      const incomingFlowData = newChatFlow.flowData
      newChatFlow.flowData = JSON.stringify({})
      const chatflow = appServer.AppDataSource.getRepository(ChatFlow).create(newChatFlow)
      const step1Results = await appServer.AppDataSource.getRepository(ChatFlow).save(chatflow)

      // step 2 - convert base64 to file paths and update the chatflow
      step1Results.flowData = await updateFlowDataWithFilePaths(step1Results.id, incomingFlowData)
      await _checkAndUpdateDocumentStoreUsage(step1Results)
      dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).save(step1Results)
    } else {
      const chatflow = appServer.AppDataSource.getRepository(ChatFlow).create(newChatFlow)
      dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).save(chatflow)
    }
    // Ghi log
    let username = 'unknown'
    if (newChatFlow.userId) {
      try {
        const user = await appServer.AppDataSource.getRepository(User).findOne({
          where: { id: newChatFlow.userId }
        })
        username = user?.username || 'unknown'
      } catch (error) {
        logger.warn('Failed to fetch username for userId:', newChatFlow.userId, error)
        username = 'unknown'
      }
    }

    await LogService.createLog({
      username: username,
      action: isUpdate ? 'UPDATE' : 'CREATE',
      target_type: 'agent',
      target_id: dbResponse.id,
      target_name: dbResponse.name
    })
    await appServer.telemetry.sendTelemetry('chatflow_created', {
      version: await getAppVersion(),
      chatflowId: dbResponse.id,
      flowGraph: getTelemetryFlowObj(JSON.parse(dbResponse.flowData)?.nodes, JSON.parse(dbResponse.flowData)?.edges)
    })
    appServer.metricsProvider?.incrementCounter(
      dbResponse?.type === 'MULTIAGENT' ? FLOWISE_METRIC_COUNTERS.AGENTFLOW_CREATED : FLOWISE_METRIC_COUNTERS.CHATFLOW_CREATED,
      { status: FLOWISE_COUNTER_STATUS.SUCCESS }
    )

    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: chatflowsService.saveChatflow - ${getErrorMessage(error)}`)
  }
}

const importChatflows = async (newChatflows: Partial<ChatFlow>[], queryRunner?: QueryRunner): Promise<any> => {
  try {
    const appServer = getRunningExpressApp()
    const repository = queryRunner ? queryRunner.manager.getRepository(ChatFlow) : appServer.AppDataSource.getRepository(ChatFlow)

    // step 1 - check whether file chatflows array is zero
    if (newChatflows.length == 0) return

    // step 2 - check whether ids are duplicate in database
    let ids = '('
    let count: number = 0
    const lastCount = newChatflows.length - 1
    newChatflows.forEach((newChatflow) => {
      ids += `'${newChatflow.id}'`
      if (lastCount != count) ids += ','
      if (lastCount == count) ids += ')'
      count += 1
    })

    const selectResponse = await repository.createQueryBuilder('cf').select('cf.id').where(`cf.id IN ${ids}`).getMany()
    const foundIds = selectResponse.map((response) => {
      return response.id
    })

    // step 3 - remove ids that are only duplicate
    const prepChatflows: Partial<ChatFlow>[] = newChatflows.map((newChatflow) => {
      let id: string = ''
      if (newChatflow.id) id = newChatflow.id
      let flowData: string = ''
      if (newChatflow.flowData) flowData = newChatflow.flowData
      if (foundIds.includes(id)) {
        newChatflow.id = undefined
        newChatflow.name += ' (1)'
      }
      newChatflow.flowData = JSON.stringify(JSON.parse(flowData))
      return newChatflow
    })

    // step 4 - transactional insert array of entities
    const insertResponse = await repository.insert(prepChatflows)

    return insertResponse
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: chatflowsService.saveChatflows - ${getErrorMessage(error)}`)
  }
}

const updateChatflow = async (chatflow: ChatFlow, updateChatFlow: ChatFlow): Promise<any> => {
  try {
    const appServer = getRunningExpressApp()
    if (updateChatFlow.flowData && containsBase64File(updateChatFlow)) {
      updateChatFlow.flowData = await updateFlowDataWithFilePaths(chatflow.id, updateChatFlow.flowData)
    }
    const newDbChatflow = appServer.AppDataSource.getRepository(ChatFlow).merge(chatflow, updateChatFlow)
    await _checkAndUpdateDocumentStoreUsage(newDbChatflow)
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).save(newDbChatflow)
    let username = 'unknown'
    if (chatflow.userId) {
      try {
        const user = await appServer.AppDataSource.getRepository(User).findOne({
          where: { id: chatflow.userId }
        })
        username = user?.username || 'unknown'
      } catch (error) {
        logger.warn('Failed to fetch username for userId:', chatflow.userId, error)
        username = 'unknown'
      }
    }
    await LogService.createLog({
      username: username,
      action: 'UPDATE',
      target_type: 'agent',
      target_id: dbResponse.id,
      target_name: dbResponse.name
    })
    // chatFlowPool is initialized only when a flow is opened
    // if the user attempts to rename/update category without opening any flow, chatFlowPool will be undefined
    if (appServer.chatflowPool) {
      // Update chatflowpool inSync to false, to build flow from scratch again because data has been changed
      appServer.chatflowPool.updateInSync(chatflow.id, false)
    }
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: chatflowsService.updateChatflow - ${getErrorMessage(error)}`)
  }
}

// Get specific chatflow via id (PUBLIC endpoint, used when sharing chatbot link)
const getSinglePublicChatflow = async (chatflowId: string): Promise<any> => {
  try {
    const appServer = getRunningExpressApp()
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).findOneBy({
      id: chatflowId
    })
    if (!dbResponse) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, `Chatflow ${chatflowId} not found`)
    }
    if (dbResponse && dbResponse.isPublish) {
      return dbResponse
    } else if (dbResponse && !dbResponse.isPublish) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, `Flow ${dbResponse?.name} chưa được phát hành`)
    }
    throw new InternalFlowiseError(StatusCodes.NOT_FOUND, `Chatflow ${chatflowId} not found`)
  } catch (error) {
    if (error instanceof InternalFlowiseError && error.statusCode === StatusCodes.UNAUTHORIZED) {
      throw error
    } else {
      throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `${getErrorMessage(error)}`)
    }
  }
}

// Get specific chatflow chatbotConfig via id (PUBLIC endpoint, used to retrieve config for embedded chat)
// Safe as public endpoint as chatbotConfig doesn't contain sensitive credential
const getSinglePublicChatbotConfig = async (chatflowId: string): Promise<any> => {
  try {
    const appServer = getRunningExpressApp()
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).findOneBy({
      id: chatflowId
    })
    if (!dbResponse) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, `Chatflow ${chatflowId} not found`)
    }
    const uploadsConfig = await utilGetUploadsConfig(chatflowId)
    // even if chatbotConfig is not set but uploads are enabled
    // send uploadsConfig to the chatbot
    if (dbResponse.chatbotConfig || uploadsConfig) {
      try {
        const parsedConfig = dbResponse.chatbotConfig ? JSON.parse(dbResponse.chatbotConfig) : {}
        return { ...parsedConfig, uploads: uploadsConfig }
      } catch (e) {
        throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error parsing Chatbot Config for Chatflow ${chatflowId}`)
      }
    }
    return 'OK'
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getSinglePublicChatbotConfig - ${getErrorMessage(error)}`
    )
  }
}

const _checkAndUpdateDocumentStoreUsage = async (chatflow: ChatFlow) => {
  const parsedFlowData: IReactFlowObject = JSON.parse(chatflow.flowData)
  const nodes = parsedFlowData.nodes
  // from the nodes array find if there is a node with name == documentStore)
  const node = nodes.length > 0 && nodes.find((node) => node.data.name === 'documentStore')
  if (!node || !node.data || !node.data.inputs || node.data.inputs['selectedStore'] === undefined) {
    await documentStoreService.updateDocumentStoreUsage(chatflow.id, undefined)
  } else {
    await documentStoreService.updateDocumentStoreUsage(chatflow.id, node.data.inputs['selectedStore'])
  }
}

const getPromptSystemList = async (): Promise<any[]> => {
  try {
    const appServer = getRunningExpressApp()
    const chatflows = await appServer.AppDataSource.getRepository(ChatFlow).find({ where: { type: 'MULTIAGENT' } })

    console.log('🚀 ~ index.ts:538 ~ getPromptSystemList ~ chatflows:', chatflows.length)
    const promptSystemList = []

    for (const chatflow of chatflows) {
      try {
        const flowData = JSON.parse(chatflow.flowData)
        if (!flowData.nodes) continue

        for (const node of flowData.nodes) {
          // Look for nodes with systemMessage in inputs
          if (node.data?.inputs?.systemMessagePrompt) {
            promptSystemList.push({
              chatflowName: chatflow.name,
              promptSystem: node.data?.inputs?.systemMessagePrompt
            })
            break
          }
        }
      } catch (error) {
        logger.error(`Error parsing flowData for chatflow ${chatflow.id}: ${getErrorMessage(error)}`)
      }
    }

    console.log('🚀 ~ index.ts:559 ~ getPromptSystemList ~ promptSystemList:', promptSystemList.length)
    return promptSystemList
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getPromptSystemList - ${getErrorMessage(error)}`
    )
  }
}

const getAllPublishChatflows = async (req: any): Promise<any[]> => {
  try {
    const type = req.query?.type as ChatflowType
    const appServer = getRunningExpressApp()
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).find({
      where: { isPublish: true, type },
      relations: ['user'],
      order: {
        updatedDate: 'DESC'
      }
    })
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getAllPublicChatflows - ${getErrorMessage(error)}`
    )
  }
}

const getAllChatFlowByGroup = async (req: any): Promise<any[]> => {
  try {
    const groupId = req.query?.groupId
    const appServer = getRunningExpressApp()

    if (!groupId) {
      StatusCodes.NOT_FOUND, `Group ${groupId} not found`
    }

    const foundGroup = await appServer.AppDataSource.getRepository(GroupUsers).findOneBy({
      id: groupId
    })
    const dbResponse = await appServer.AppDataSource.getRepository(ChatFlow).find({
      where: { groupname: foundGroup?.groupname },
      order: {
        updatedDate: 'DESC'
      }
    })
    return dbResponse
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatflowsService.getAllChatFlowByGroup - ${getErrorMessage(error)}`
    )
  }
}

export default {
  checkIfChatflowIsValidForStreaming,
  checkIfChatflowIsValidForUploads,
  deleteChatflow,
  getAllChatflows,
  getAllPublicChatflows,
  getChatflowByApiKey,
  getChatflowById,
  saveChatflow,
  importChatflows,
  updateChatflow,
  getSinglePublicChatflow,
  getSinglePublicChatbotConfig,
  getControlChatflowsOfAdmin,
  getControlChatflowsOfAdminGroup,
  getPersonalChatflows,
  getPromptSystemList,
  getChatflowByIds,
  getAllPublishChatflows,
  getAllChatFlowByGroup
}
